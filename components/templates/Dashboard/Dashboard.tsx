'use client';

import { UserMenuDropdownProps } from '@/components/molecules/UserMenuDropdown/UserMenuDropdown';
import Sidebar from '@/components/organisms/Sidebar/Sidebar';
import BottomNavigation from '@/components/organisms/BottomNavigation/BottomNavigation';
import { cn } from '@/utils/cn';
import { useState } from 'react';
import type { ComponentProps } from 'react';
import type { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse

export interface PageContainerProps extends ComponentProps<'div'> {
  pageContainerType?: 'default' | 'gutterless' | 'contained';
  sidebarItems: { label: string; href: string; icon?: React.ReactNode }[];
  children: React.ReactNode;
  userMenuDropdownProps: UserMenuDropdownProps;
  schoolInfo?: ISchoolResponse | null; // Changed to ISchoolResponse
}

const DashboardTemplate = ({
  pageContainerType = 'default',
  children,
  sidebarItems = [],
  userMenuDropdownProps,
  schoolInfo,
}: PageContainerProps) => {
  const isContained = pageContainerType === 'contained';
  const isAuth = sidebarItems.length > 0;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  // Convert sidebar items to bottom navigation format
  const bottomNavItems = sidebarItems.map(item => ({
    label: item.label,
    href: item.href,
    icon: item.icon || null
  }));

  if (!isAuth) {
    // For pages without sidebar (like auth pages), render without drawer
    return (
      <div className="min-h-screen">
        <main className="h-full">
          <div
            className={cn(
              'page-container flex-col flex h-full flex-auto relative bg-slate-50',
              pageContainerType !== 'gutterless' ? 'pt-6 pb-8.5 px-6' : '',
              isContained && 'container mx-auto'
            )}
          >
            {isContained ? (
              <div className="container mx-auto">
                {children}
              </div>
            ) : (
              children
            )}
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="drawer lg:drawer-open">
      {/* Hidden checkbox to control drawer state */}
      <input
        id="dashboard-drawer"
        type="checkbox"
        className="drawer-toggle"
        checked={isDrawerOpen}
        onChange={handleMenuToggle}
      />

      {/* Main content area */}
      <div className="drawer-content flex flex-col min-h-screen">
        {/* Mobile header with menu button */}
        <div className="navbar bg-base-100 lg:hidden border-b border-base-300">
          <div className="flex-none">
            <label
              htmlFor="dashboard-drawer"
              className="btn btn-square btn-ghost"
              aria-label="Open menu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="inline-block w-6 h-6 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </label>
          </div>
          <div className="flex-1 px-2 mx-2">
            <span className="text-lg font-semibold">Dashboard</span>
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 pb-16 lg:pb-0">
          <div
            className={cn(
              'page-container flex-col flex h-full flex-auto relative bg-slate-50',
              pageContainerType !== 'gutterless' ? 'pt-6 pb-8.5 px-6' : '',
              isContained && 'container mx-auto'
            )}
          >
            {isContained ? (
              <div className="container mx-auto">
                {children}
              </div>
            ) : (
              children
            )}
          </div>
        </main>

        {/* Bottom Navigation for mobile */}
        <BottomNavigation
          items={bottomNavItems}
          onMenuToggle={handleMenuToggle}
        />
      </div>

      {/* Sidebar */}
      <div className="drawer-side">
        <label
          htmlFor="dashboard-drawer"
          className="drawer-overlay"
          onClick={handleDrawerClose}
        />
        <Sidebar
          items={sidebarItems}
          userMenuDropdownProps={userMenuDropdownProps}
          schoolInfo={schoolInfo}
          onClose={handleDrawerClose}
        />
      </div>
    </div>
  );
};

export default DashboardTemplate;
