'use client';

import * as React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { getFileRenderUrl } from '@/utils/fileUtils';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';
import { UserMenuDropdown, UserMenuDropdownProps } from '@/components/molecules/UserMenuDropdown/UserMenuDropdown';
import { EUserRole } from '@/config/enums/user';
import { 
  Edit2, 
  MapPin, 
  Phone,
  Mail,
  FileText,
  Settings,
  CheckCircle2
} from 'lucide-react';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';
import { ISchoolResponse } from '@/apis/schoolApi';

export interface SidebarItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}

export interface SidebarProps {
  items: SidebarItem[];
  className?: string;
  userMenuDropdownProps?: UserMenuDropdownProps;
  schoolInfo?: ISchoolResponse | null;
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ items, className, userMenuDropdownProps, schoolInfo, onClose }) => {
  const pathname = usePathname();
  const { data: session, update: updateSession } = useSession();
  const userRole = session?.user?.role;
  const showSchoolInfo = schoolInfo && (userRole === EUserRole.TEACHER || userRole === EUserRole.SCHOOL_MANAGER || userRole === EUserRole.SUPER_ADMIN || userRole === EUserRole.INDEPENDENT_TEACHER);
  const canEditSchool = userRole === EUserRole.SCHOOL_MANAGER || userRole === EUserRole.SUPER_ADMIN || userRole === EUserRole.INDEPENDENT_TEACHER;

  const [isSchoolModalOpen, setIsSchoolModalOpen] = React.useState(false);

  const [isHoveringSchoolCard, setIsHoveringSchoolCard] = React.useState(false);

  const handleOpenEditModal = () => {
    if (canEditSchool) {
      // For independent teachers, redirect to /my-school page instead of opening modal
      if (userRole === EUserRole.INDEPENDENT_TEACHER) {
        window.location.href = '/my-school';
      } else {
        // For other roles (SCHOOL_MANAGER, SUPER_ADMIN), use the modal
        setIsSchoolModalOpen(true);
      }
    }
  };

  const handleCloseSchoolModal = () => {
    setIsSchoolModalOpen(false);
  };

  const handleSchoolUpdateSuccess = async (updatedSchoolData: ISchoolResponse | { id: string; name: string }) => {
    if ('address' in updatedSchoolData) {
      await updateSession({
        ...session,
        user: {
          ...session?.user,
          schoolName: updatedSchoolData.name,
          school: {
            ...(session?.user?.school || {}),
            name: updatedSchoolData.name,
            address: updatedSchoolData.address,
            phoneNumber: updatedSchoolData.phoneNumber,
            registeredNumber: updatedSchoolData.registeredNumber,
            email: updatedSchoolData.email,
            // Include brand information in the session update
            brand: updatedSchoolData.brand ? {
              id: updatedSchoolData.brand.id,
              logo: updatedSchoolData.brand.logo,
              color: updatedSchoolData.brand.color,
              image: updatedSchoolData.brand.image,
            } : session?.user?.school?.brand,
          }
        },
      });
    }
    handleCloseSchoolModal();
  };

  const handleSchoolModalError = (message: string) => {
    console.error("SchoolModal Error:", message);
  };

  if (items.length === 0) return <></>;

  return (
    <aside
      className={cn(
        'bg-base-200 text-base-content min-h-full w-80 flex flex-col',
        className
      )}
    >
      {/* Header with logo */}
      <div className="flex items-center px-4 py-5 border-b border-gray-100 bg-section-bg-accent">
        <div className="w-auto">
          <Icon variant="dashboard-icon" className="h-8 w-auto" size={62} />
        </div>
      </div>

      {/* Navigation section */}
      <div className="px-4 py-4">
        <div className="text-xs uppercase font-semibold text-text-secondary tracking-wider mb-3">
          OVERVIEW
        </div>
        <nav className="flex flex-col gap-1">
          {items.map((item, index) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={index}
                href={item.href}
                onClick={() => onClose?.()}
                className={cn(
                  'flex items-center rounded-lg transition-all duration-200 ease-in-out px-3 py-2.5 gap-3 group',
                  isActive
                    ? 'bg-primary/10 text-primary shadow-sm border border-primary/20'
                    : 'text-base-content hover:bg-base-300 hover:text-primary'
                )}
              >
                {item.icon && (
                  <span className={cn(
                    "flex-shrink-0 w-5 h-5 transition-transform duration-200",
                    isActive ? "scale-110" : "group-hover:scale-105"
                  )}>
                    {item.icon}
                  </span>
                )}
                <span className="text-sm font-medium">{item.label}</span>
                {isActive && (
                  <div className="ml-auto">
                    <CheckCircle2 size={16} className="text-primary" />
                  </div>
                )}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Spacer to push content to bottom */}
      <div className="flex-grow"></div>

      {/* School Information Card */}
      {showSchoolInfo && schoolInfo && (
        <div className="mx-2 mb-3"> {/* Reduced margins */}
          <div
            className={cn(
              "bg-section-bg-neutral-alt rounded-lg shadow-sm border transition-all duration-300 overflow-hidden",
              isHoveringSchoolCard
                ? "border-gray-300 shadow-md transform scale-[1.01]"
                : "border-gray-200"
            )}
            onMouseEnter={() => setIsHoveringSchoolCard(true)}
            onMouseLeave={() => setIsHoveringSchoolCard(false)}
          >
            {/* School header with branding */}
            <div className="relative p-4 pb-3">
              {/* Enhanced brand color accent */}
              {schoolInfo.brand?.color && (
                <div
                  className="absolute top-0 left-0 right-0 h-1 rounded-t-lg"
                  style={{ backgroundColor: schoolInfo.brand.color }}
                />
              )}

              {/* Enhanced School Logo above name - Clean layout without flex */}
              {schoolInfo.brand?.logo && (
                <div className="text-center">
                  {(() => {
                    try {
                      const logoUrl = getFileRenderUrl(schoolInfo.brand.logo);
                      return logoUrl ? (
                        <div className="relative group inline-block">
                          {/* Logo container with enhanced styling */}
                          <div className="w-20 h-20 rounded-xl overflow-hidden border-2 border-white shadow-lg bg-gradient-to-br from-white to-gray-50 p-2 transition-all duration-200 group-hover:shadow-xl group-hover:scale-105">
                            <Image
                              src={logoUrl}
                              alt={`${schoolInfo.name} logo`}
                              width={64}
                              height={64}
                              className="object-contain w-full h-full"
                              onError={(e) => {
                                console.error('Failed to load school logo above name:', logoUrl);
                                (e.target as HTMLImageElement).style.display = 'none';
                              }}
                            />
                          </div>

                          {/* Brand color accent ring */}
                          {schoolInfo.brand?.color && (
                            <div
                              className="absolute inset-0 rounded-xl border-2 opacity-30"
                              style={{ borderColor: schoolInfo.brand.color }}
                            />
                          )}

                          {/* Subtle glow effect */}
                          <div
                            className="absolute inset-0 rounded-xl opacity-20 blur-sm -z-10"
                            style={{
                              backgroundColor: schoolInfo.brand?.color || '#3B82F6',
                              transform: 'scale(1.1)'
                            }}
                          />
                        </div>
                      ) : null;
                    } catch (error) {
                      console.error('Error processing school logo URL above name:', schoolInfo.brand.logo, error);
                      return null;
                    }
                  })()}
                </div>
              )}

              {/* School Name - Centered below logo */}
              <div className="text-center">
                <h3 className="text-lg font-bold text-text-primary">{schoolInfo.name}</h3>
              </div>

              {/* Edit button - Positioned in top right */}
              {canEditSchool && (
                <div className="absolute top-4 right-4">
                  <button
                    onClick={handleOpenEditModal}
                    className={cn(
                      "p-2 rounded-lg transition-all duration-200 group",
                      isHoveringSchoolCard
                        ? "bg-interactive-hover-bg-light text-text-primary shadow-sm"
                        : "bg-background-default/70 text-text-secondary hover:bg-interactive-hover-bg-light hover:text-text-primary"
                    )}
                    aria-label="Edit school information"
                  >
                    <Settings size={16} className="transition-transform duration-200 group-hover:rotate-90" />
                  </button>
                </div>
              )}
            </div>

            {/* School details */}
            <div className="px-4 pb-4 space-y-2">
              {schoolInfo.address && (
                <div className="flex items-start group/item">
                  <div className="flex-shrink-0 w-3.5 h-3.5 flex items-center justify-center mr-2 mt-0.5">
                    <MapPin size={12} className="text-text-secondary group-hover/item:text-text-primary transition-colors" />
                  </div>
                  <p className="text-xs text-text-secondary leading-snug group-hover/item:text-text-primary transition-colors">
                    {schoolInfo.address}
                  </p>
                </div>
              )}

              {schoolInfo.phoneNumber && (
                <div className="flex items-center group/item">
                  <div className="flex-shrink-0 w-3.5 h-3.5 flex items-center justify-center mr-2">
                    <Phone size={12} className="text-text-secondary group-hover/item:text-text-primary transition-colors" />
                  </div>
                  <p className="text-xs text-text-secondary group-hover/item:text-text-primary transition-colors">
                    {schoolInfo.phoneNumber}
                  </p>
                </div>
              )}

              {schoolInfo.email && (
                <div className="flex items-center group/item">
                  <div className="flex-shrink-0 w-3.5 h-3.5 flex items-center justify-center mr-2">
                    <Mail size={12} className="text-text-secondary group-hover/item:text-text-primary transition-colors" />
                  </div>
                  <p className="text-xs text-text-secondary group-hover/item:text-text-primary transition-colors truncate">
                    {schoolInfo.email}
                  </p>
                </div>
              )}

              {schoolInfo.registeredNumber && (
                <div className="flex items-center group/item">
                  <div className="flex-shrink-0 w-3.5 h-3.5 flex items-center justify-center mr-2">
                    <FileText size={12} className="text-text-secondary group-hover/item:text-text-primary transition-colors" />
                  </div>
                  <p className="text-xs text-text-secondary group-hover/item:text-text-primary transition-colors">
                    <span className="text-text-secondary">Reg:</span> {schoolInfo.registeredNumber}
                  </p>
                </div>
              )}

              {/* Quick edit hint */}
              {canEditSchool && isHoveringSchoolCard && (
                <div className="mt-2 pt-1.5 border-t border-gray-200">
                  <p className="text-xs text-text-secondary font-medium flex items-center gap-1">
                    <Edit2 size={10} />
                    Click settings to edit school info
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* User dropdown at bottom of sidebar */}
      {userMenuDropdownProps && (
        <div className="mt-auto w-full border-t border-gray-100 px-3 py-3 bg-background-subtle/50">
          <UserMenuDropdown
            userName={userMenuDropdownProps.userName}
            userEmail={userMenuDropdownProps.userEmail}
            className="w-full"
          />
        </div>
      )}
      {/* School Modal (for editing) - Only for SCHOOL_MANAGER and SUPER_ADMIN */}
      {schoolInfo && canEditSchool && userRole !== EUserRole.INDEPENDENT_TEACHER && (
        <SchoolModal
          isOpen={isSchoolModalOpen}
          onClose={handleCloseSchoolModal}
          schoolToEdit={schoolInfo}
          onSuccess={handleSchoolUpdateSuccess}
          onError={handleSchoolModalError}
        />
      )}
    </aside>
  );
};

export default Sidebar;
