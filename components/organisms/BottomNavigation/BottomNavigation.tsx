'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/utils/cn';
import { HomeIcon, ClipboardListIcon, Building2Icon, UserIcon, MenuIcon } from 'lucide-react';
import type { ComponentProps } from 'react';

export interface BottomNavigationItem {
  label: string;
  href: string;
  icon: React.ReactNode;
}

export interface BottomNavigationProps extends ComponentProps<'div'> {
  items: BottomNavigationItem[];
  onMenuToggle?: () => void;
}

const BottomNavigation = ({ items, onMenuToggle, className, ...props }: BottomNavigationProps) => {
  const pathname = usePathname();

  // Get the most important items for bottom navigation (max 4-5 items)
  const getBottomNavItems = () => {
    const bottomNavItems: BottomNavigationItem[] = [];
    
    // Always add menu toggle as first item
    bottomNavItems.push({
      label: 'Menu',
      href: '#',
      icon: <MenuIcon className="w-5 h-5" />
    });

    // Add up to 3 most important navigation items
    const importantItems = items.slice(0, 3);
    bottomNavItems.push(...importantItems);

    return bottomNavItems;
  };

  const bottomNavItems = getBottomNavItems();

  if (items.length === 0) return null;

  return (
    <div 
      className={cn(
        'dock dock-sm fixed bottom-0 left-0 right-0 z-50 bg-base-100 border-t border-base-300 lg:hidden',
        className
      )}
      {...props}
    >
      {bottomNavItems.map((item, index) => {
        const isActive = pathname === item.href;
        const isMenuButton = item.label === 'Menu';
        
        if (isMenuButton) {
          return (
            <button
              key={index}
              onClick={onMenuToggle}
              className={cn(
                'dock-item flex flex-col items-center justify-center p-2 min-h-[60px] transition-colors',
                'hover:bg-base-200 active:bg-base-300'
              )}
              aria-label="Open menu"
            >
              <span className="flex-shrink-0 mb-1">
                {item.icon}
              </span>
              <span className="text-xs font-medium text-base-content/70">
                {item.label}
              </span>
            </button>
          );
        }

        return (
          <Link
            key={index}
            href={item.href}
            className={cn(
              'dock-item flex flex-col items-center justify-center p-2 min-h-[60px] transition-colors',
              isActive 
                ? 'dock-active bg-primary/10 text-primary' 
                : 'text-base-content/70 hover:bg-base-200 hover:text-base-content'
            )}
          >
            <span className={cn(
              'flex-shrink-0 mb-1 transition-transform',
              isActive ? 'scale-110' : ''
            )}>
              {item.icon}
            </span>
            <span className="text-xs font-medium">
              {item.label}
            </span>
          </Link>
        );
      })}
    </div>
  );
};

export default BottomNavigation;
